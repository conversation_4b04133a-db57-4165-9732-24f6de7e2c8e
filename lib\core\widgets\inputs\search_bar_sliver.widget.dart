import 'package:kairos/core/widgets/dialogs/date_filter_dialog.widget.dart';
import 'package:flutter/material.dart';

class SearchBarSliver extends SliverPersistentHeaderDelegate {
  final double extentHeight;
  final TextEditingController searchController;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<Map<String, String>>? onDateFilterChanged;
  final VoidCallback? onClearDateFilter;
  final String? hintText;
  final bool showYear;
  final bool hasActiveFilter;
  final bool showDateFilter;

  SearchBarSliver({
    required this.extentHeight,
    required this.searchController,
    this.hintText,
    this.onSearchChanged,
    this.onDateFilterChanged,
    this.onClearDateFilter,
    this.showYear = false,
    this.hasActiveFilter = false,
    this.showDateFilter = true,
  });

  @override
  double get maxExtent => extentHeight;

  @override
  double get minExtent => extentHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor, // Or a suitable background color
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Material(
          color: Colors.transparent, // Ensure the Material widget itself is transparent
          child: TextField(
            controller: searchController,
            onChanged: onSearchChanged,
            decoration: InputDecoration(
              hintText: hintText ?? "Rechercher...",
              isDense: true,
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Calendar button
                  if(showDateFilter) IconButton(
                    onPressed: () async {
                      final result = await showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return DateFilterDialog(showYear: showYear);
                        },
                      );
                      debugPrint("result: $result");

                      // Validate and process the result
                      if (result != null && result is Map<String, dynamic>) {
                        final String? startDate = result['startDate'] as String?;
                        final String? endDate = result['endDate'] as String?;
                        debugPrint("startDate: $startDate, endDate: $endDate");
                        // Validate that both dates are present and not empty
                        if (startDate != null && startDate.isNotEmpty &&
                            endDate != null && endDate.isNotEmpty) {
                          // Call the date filter callback with validated dates
                          if (onDateFilterChanged != null) {
                            onDateFilterChanged!({
                              'startDate': startDate,
                              'endDate': endDate,
                            });
                          }
                        } else if (endDate != null && endDate.isNotEmpty && showYear) {
                          // Call the date filter callback with validated dates
                          debugPrint("endDate --->: $endDate");
                          if (onDateFilterChanged != null) {
                            onDateFilterChanged!({
                              'endDate': endDate,
                            });
                          }
                        } else {
                          // Show error message if dates are missing
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Veuillez sélectionner les deux dates pour filtrer'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      }
                    },
                    icon: Icon(Icons.calendar_month_sharp, color: Colors.grey[600]),
                  ),
                  // Clear filter button (only show when filter is active)
                  if (hasActiveFilter)
                    IconButton(
                      onPressed: onClearDateFilter ?? onClearDateFilter,
                      icon: Icon(Icons.cancel_rounded, color: Colors.red[600]),
                      tooltip: 'Effacer le filtre de date',
                    ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide(color: Colors.grey[300]!, width: 1.0),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: EdgeInsets.symmetric(vertical: 0.0, horizontal: 16.0),
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    // Rebuild if max/min extent changes or if search controller/callback changes
    final SearchBarSliver oldSearchBarSliver = oldDelegate as SearchBarSliver;
    return oldDelegate.maxExtent != maxExtent ||
        oldDelegate.minExtent != minExtent ||
        oldSearchBarSliver.hintText != hintText ||
        oldSearchBarSliver.searchController != searchController ||
        oldSearchBarSliver.onSearchChanged != onSearchChanged ||
        oldSearchBarSliver.onDateFilterChanged != onDateFilterChanged ||
        oldSearchBarSliver.onClearDateFilter != onClearDateFilter ||
        oldSearchBarSliver.hasActiveFilter != hasActiveFilter;
  }
}
