import '../../domain/entities/carte_virtuelle_entity.dart';

/// CarteVirtuelle model representing virtual card data from the data layer
class CarteVirtuelleModel extends CarteVirtuelleEntity {
  const CarteVirtuelleModel({
    required super.codeUtilisateur,
    required super.prenom,
    required super.nom,
    required super.profil,
    required String super.photo,
    required super.programmeEnCours,
    required super.anneeScolaireEnCours,
    required super.niveauEnCours,
    required super.classeEnCours,
    required super.dateDerniereInscription,
    required super.montantAPayer,
    required super.montantPaye,
  });

  factory CarteVirtuelleModel.fromJson(Map<String, dynamic> json) {
    return CarteVirtuelleModel(
      codeUtilisateur: json['codeUtilisateur'] as String,
      prenom: json['prenom'] as String,
      nom: json['nom'] as String,
      profil: json['profil'] as String,
      photo: json['photo']??'',
      programmeEnCours: json['programmeEnCours'] as String,
      anneeScolaireEnCours: json['anneeScolaireEnCours'] as String,
      niveauEnCours: json['niveauEnCours'] as String,
      classeEnCours: json['classeEnCours'] as String,
      dateDerniereInscription: json['dateDerniereInscription'] as String,
      montantAPayer: (json['montantAPayer'] as num).toDouble(),
      montantPaye: (json['montantPaye'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'codeUtilisateur': codeUtilisateur,
      'prenom': prenom,
      'nom': nom,
      'profil': profil,
      'photo': photo,
      'programmeEnCours': programmeEnCours,
      'anneeScolaireEnCours': anneeScolaireEnCours,
      'niveauEnCours': niveauEnCours,
      'classeEnCours': classeEnCours,
      'dateDerniereInscription': dateDerniereInscription,
      'montantAPayer': montantAPayer,
      'montantPaye': montantPaye,
    };
  }
}