


import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ListeEmptyAlert extends StatelessWidget{
  const ListeEmptyAlert({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              SvgPicture.asset("assets/icons/icon_aucun_etablissement.svg"),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text("Aucun établissement n'a été activé pour ce numéro de téléphone. Veuillez activer un nouvel établissement.", textAlign: TextAlign.center, style: TextStyle(fontSize: 20, color: Colors.orange),),
              )
            ],),
          )
          ;
  }
}