import 'package:equatable/equatable.dart';

/// CarteVirtuelle entity representing virtual card data in the domain layer
class CarteVirtuelleEntity extends Equatable {
  final String codeUtilisateur;
  final String prenom;
  final String nom;
  final String profil;
  final String? photo;
  final String programmeEnCours;
  final String anneeScolaireEnCours;
  final String niveauEnCours;
  final String classeEnCours;
  final String dateDerniereInscription;
  final double montantAPayer;
  final double montantPaye;

  const CarteVirtuelleEntity({
    required this.codeUtilisateur,
    required this.prenom,
    required this.nom,
    required this.profil,
    this.photo,
    required this.programmeEnCours,
    required this.anneeScolaireEnCours,
    required this.niveauEnCours,
    required this.classeEnCours,
    required this.dateDerniereInscription,
    required this.montantAPayer,
    required this.montantPaye,
  });

  factory CarteVirtuelleEntity.fromJson(Map<String, dynamic> json) {
    return CarteVirtuelleEntity(
      codeUtilisateur: json['codeUtilisateur'] as String,
      prenom: json['prenom'] as String,
      nom: json['nom'] as String,
      profil: json['profil'] as String,
      photo: json['photo'] as String?,
      programmeEnCours: json['programmeEnCours'] as String,
      anneeScolaireEnCours: json['anneeScolaireEnCours'] as String,
      niveauEnCours: json['niveauEnCours'] as String,
      classeEnCours: json['classeEnCours'] as String,
      dateDerniereInscription: json['dateDerniereInscription'] as String,
      montantAPayer: (json['montantAPayer'] as num).toDouble(),
      montantPaye: (json['montantPaye'] as num).toDouble(),
    );
  }

  @override
  List<Object?> get props => [
        codeUtilisateur,
        prenom,
        nom,
        profil,
        photo,
        programmeEnCours,
        anneeScolaireEnCours,
        niveauEnCours,
        classeEnCours,
        dateDerniereInscription,
        montantAPayer,
        montantPaye,
      ];
}